import os
import win32com.client as win32


def doc_to_txt(doc_path, txt_path=None):
    """
    将 .doc 文件转换为 .txt 文件（Windows 专用）

    参数:
        doc_path (str): 输入的 .doc 文件路径
        txt_path (str, 可选): 输出的 .txt 文件路径，默认与输入文件同目录
    """
    if txt_path is None:
        txt_path = os.path.splitext(doc_path)[0] + ".txt"

    try:
        # 启动 Word 应用程序
        word = win32.Dispatch("Word.Application")
        word.Visible = False  # 不显示 Word 界面

        # 打开 .doc 文件
        doc = word.Documents.Open(doc_path)

        # 提取全部文本
        text = doc.Content.Text

        # 写入 .txt 文件
        with open(txt_path, "w", encoding="utf-8") as f:
            f.write(text)

        print(f"转换成功: {doc_path} → {txt_path}")

    except Exception as e:
        print(f"转换失败: {e}")

    finally:
        # 关闭 Word 文档和应用
        doc.Close(False)
        word.Quit()


# 示例用法
if __name__ == "__main__":
    doc_to_txt(
        "D:\\WeChat\\WeChat Files\\wxid_9o653ngpnjpq22\\FileStorage\\File\\2025-06\\92e45d3c594715fe385df640bd8a371b_97bd7de3f79f0c6d7be0296f527a6bfc_8.doc",
        "题库.txt",
    )  # 替换为你的 .doc 文件路径
